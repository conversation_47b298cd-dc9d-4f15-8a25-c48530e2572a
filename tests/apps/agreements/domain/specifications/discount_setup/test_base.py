import pytest

from nga.apps.agreements.domain.exceptions import DiscountValidationError
from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.abstract import ContainerDiscountSpecification
from nga.apps.agreements.domain.specifications.discount_setup.base import AbstractDiscountSetupSpecification
from nga.apps.agreements.domain.specifications.discount_setup.field_validators import (
    credit_note_eoa_settlement_method,
    no_balancing,
    param_basis_is_value,
    param_basis_value_is_required,
    service_types_with_combo,
    financial_threshold_only_for_sop_financial,
    sub_discounts_only_for_sop_financial,
    above_commitment_rate_only_for_sub_discounts,
)
from tests.apps.agreements.fakes import FakeDiscountSpecification, FakeErrorDiscountSpecification
from tests.factories.agreements.domain import DiscountFactory


class TestContainerDiscountSpecification:
    def test_with_valid_spec(self):
        discount = DiscountFactory()

        spec = ContainerDiscountSpecification(specifications=[FakeDiscountSpecification()])

        spec.verify(discount)

    def test_with_invalid_and_valid_specs(self):
        discount = DiscountFactory()

        spec = ContainerDiscountSpecification(
            specifications=[FakeDiscountSpecification(), FakeErrorDiscountSpecification()]
        )

        with pytest.raises(DiscountValidationError):  # Failed on the second specification
            spec.verify(discount)

    def test_without_valid_specs(self):
        discount = DiscountFactory()

        spec = ContainerDiscountSpecification(
            specifications=[FakeErrorDiscountSpecification(), FakeErrorDiscountSpecification()]
        )

        with pytest.raises(DiscountValidationError) as exc_info:
            spec.verify(discount)

        assert "fake spec error" in exc_info.value.message

    def test_when_there_is_not_setup_for_discount(self):
        checked_discount = DiscountFactory()

        class _FakeSetupSpec(AbstractDiscountSetupSpecification):
            def verify(self, discount: Discount) -> None:
                raise DiscountValidationError("Error message")

        spec = ContainerDiscountSpecification(specifications=[_FakeSetupSpec()])

        with pytest.raises(DiscountValidationError):
            spec.verify(checked_discount)


class TestAbstractDiscountSetupSpecification:
    def test_discount_validators(self):
        assert AbstractDiscountSetupSpecification.discount_validators == (
            service_types_with_combo,
            credit_note_eoa_settlement_method,
            financial_threshold_only_for_sop_financial,
            sub_discounts_only_for_sop_financial,
            above_commitment_rate_only_for_sub_discounts,
        )

    def test_discount_parameter_validators(self):
        assert AbstractDiscountSetupSpecification.discount_parameter_validators == (
            param_basis_is_value,
            param_basis_value_is_required,
            no_balancing,            
        )
