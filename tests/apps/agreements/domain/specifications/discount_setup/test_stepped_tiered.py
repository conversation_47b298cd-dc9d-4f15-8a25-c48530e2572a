from decimal import Decimal

import pytest

from nga.apps.agreements.domain.exceptions import DiscountValidationError
from nga.apps.agreements.domain.specifications.discount_setup.field_validators import (
    bound_type_is_volume,
    credit_note_eoa_settlement_method,
    no_balancing,
    param_basis_is_value,
    param_basis_value_is_required,
    service_types_with_combo,
    financial_threshold_only_for_sop_financial,
    above_commitment_rate_only_for_sub_discounts,
    sub_discounts_only_for_sop_financial,
)
from nga.apps.agreements.domain.specifications.discount_setup.stepped_tiered import (
    SteppedTieredDiscountSetupSpecification,
    stepped_tiered_bounds,
)
from tests.factories.agreements.domain import (
    SREDiscountParameterFactory,
    SteppedTieredDiscountFactory,
    SteppedTieredDiscountParameterFactory,
)


class TestSteppedTieredDiscountSetupSpecification:
    def test_discount_validators(self):
        assert SteppedTieredDiscountSetupSpecification.discount_validators == (
            service_types_with_combo,
            credit_note_eoa_settlement_method, 
            financial_threshold_only_for_sop_financial,
            above_commitment_rate_only_for_sub_discounts,
            sub_discounts_only_for_sop_financial,
                       
        )

    def test_discount_parameter_validators(self):
        expected_validators = (
            param_basis_is_value,
            param_basis_value_is_required,
            no_balancing,
            bound_type_is_volume,            
        )

        actual_validators = SteppedTieredDiscountSetupSpecification.discount_parameter_validators

        assert actual_validators == expected_validators

    @classmethod
    def verify_discount(cls, discount) -> None:
        SteppedTieredDiscountSetupSpecification().verify(discount)

    def test_when_ok(self):
        discount = SteppedTieredDiscountFactory()

        self.verify_discount(discount)

    def test_violates_by_calculation_type(self):
        discount = SteppedTieredDiscountFactory(
            parameters=[SREDiscountParameterFactory(), SteppedTieredDiscountParameterFactory()]
        )

        with pytest.raises(DiscountValidationError) as exc_info:
            self.verify_discount(discount)

        assert "is invalid for STEPPED_TIERED setup" in exc_info.value.message


class TestSteppedTieredBoundsValidator:
    def test_with_one_correct_tier(self):
        param = SteppedTieredDiscountParameterFactory(lower_bound=Decimal("0"), upper_bound=None)

        stepped_tiered_bounds([param])

    def test_with_one_tier_with_upper_bound_set(self):
        param = SteppedTieredDiscountParameterFactory(lower_bound=Decimal("0"), upper_bound=Decimal("34"))

        stepped_tiered_bounds([param])

    @pytest.mark.parametrize("lower_bound", [Decimal("34"), None])
    def test_when_first_tier_lower_bound_is_not_zero(self, lower_bound):
        param = SteppedTieredDiscountParameterFactory(lower_bound=lower_bound, upper_bound=None)

        with pytest.raises(DiscountValidationError) as exc_info:
            stepped_tiered_bounds([param])

        assert "First tier" in exc_info.value.message

    def test_when_two_valid_tiers(self):
        first_tier = SteppedTieredDiscountParameterFactory(upper_bound=Decimal("34"))
        last_tier = SteppedTieredDiscountParameterFactory(lower_bound=Decimal("34"), upper_bound=None)

        stepped_tiered_bounds([first_tier, last_tier])

    def test_when_two_tiers_and_first_tier_is_without_upper_bound(self):
        first_tier = SteppedTieredDiscountParameterFactory(upper_bound=None)
        last_tier = SteppedTieredDiscountParameterFactory(lower_bound=Decimal("34"), upper_bound=None)

        with pytest.raises(DiscountValidationError) as exc_info:
            stepped_tiered_bounds([first_tier, last_tier])

        assert "First tier must have upper bound" in exc_info.value.message

    def test_when_three_tiers_ok(self):
        first_tier = SteppedTieredDiscountParameterFactory(upper_bound=Decimal("34"))
        n_tier = SteppedTieredDiscountParameterFactory(lower_bound=Decimal("34"), upper_bound=Decimal("44"))
        last_tier = SteppedTieredDiscountParameterFactory(lower_bound=Decimal("44"), upper_bound=None)

        stepped_tiered_bounds([first_tier, n_tier, last_tier])

    @pytest.mark.parametrize(
        "lower_bound,upper_bound",
        [
            (Decimal("34"), None),
            (None, Decimal("34")),
            (None, None),
        ],
    )
    def test_when_n_tier_without_bounds(self, lower_bound, upper_bound):
        first_tier = SteppedTieredDiscountParameterFactory(upper_bound=Decimal("34"))
        n_tier = SteppedTieredDiscountParameterFactory(lower_bound=lower_bound, upper_bound=upper_bound)
        last_tier = SteppedTieredDiscountParameterFactory(lower_bound=Decimal("44"), upper_bound=None)

        with pytest.raises(DiscountValidationError) as exc_info:
            stepped_tiered_bounds([first_tier, n_tier, last_tier])

        assert "N-Tier must have filled bounds" in exc_info.value.message

    def test_when_last_tier_without_lower_bound(self):
        first_tier = SteppedTieredDiscountParameterFactory(upper_bound=Decimal("34"))
        last_tier = SteppedTieredDiscountParameterFactory(lower_bound=None, upper_bound=None)

        with pytest.raises(DiscountValidationError) as exc_info:
            stepped_tiered_bounds([first_tier, last_tier])

        assert "Last tier must have lower bound" in exc_info.value.message

    def test_when_first_tier_lower_bound_is_less_then_upper_bound(self):
        param = SteppedTieredDiscountParameterFactory(lower_bound=Decimal("0"), upper_bound=Decimal("-10"))

        with pytest.raises(DiscountValidationError) as exc_info:
            stepped_tiered_bounds([param])

        assert "First tier lower bound must be less then upper bound" in exc_info.value.message

    def test_when_last_tier_lower_bound_does_not_equal_to_first_tier_upper_bound(self):
        first_tier = SteppedTieredDiscountParameterFactory(upper_bound=Decimal("34"))
        last_tier = SteppedTieredDiscountParameterFactory(lower_bound=Decimal("35"), upper_bound=None)

        with pytest.raises(DiscountValidationError) as exc_info:
            stepped_tiered_bounds([first_tier, last_tier])

        assert "Last tier lower bound must equal to previous upper bound" in exc_info.value.message

    def test_when_n_tier_lower_bound_does_not_equal_to_first_tier_upper_bound(self):
        first_tier = SteppedTieredDiscountParameterFactory(upper_bound=Decimal("34"))
        n_tier = SteppedTieredDiscountParameterFactory(lower_bound=Decimal("35"), upper_bound=Decimal("36"))
        last_tier = SteppedTieredDiscountParameterFactory(lower_bound=Decimal("44"), upper_bound=None)

        with pytest.raises(DiscountValidationError) as exc_info:
            stepped_tiered_bounds([first_tier, n_tier, last_tier])

        assert "N-tier lower bound must equal to previous tier upper bound" in exc_info.value.message

    def test_when_n_tier_lower_bound_does_not_equal_to_previous_n_tier_upper_bound(self):
        first_tier = SteppedTieredDiscountParameterFactory(upper_bound=Decimal("34"))
        n_tier1 = SteppedTieredDiscountParameterFactory(lower_bound=Decimal("34"), upper_bound=Decimal("40"))
        n_tier2 = SteppedTieredDiscountParameterFactory(lower_bound=Decimal("41"), upper_bound=Decimal("42"))
        last_tier = SteppedTieredDiscountParameterFactory(lower_bound=Decimal("42"), upper_bound=None)

        with pytest.raises(DiscountValidationError) as exc_info:
            stepped_tiered_bounds([first_tier, n_tier1, n_tier2, last_tier])

        assert "N-tier lower bound must equal to previous tier upper bound" in exc_info.value.message
